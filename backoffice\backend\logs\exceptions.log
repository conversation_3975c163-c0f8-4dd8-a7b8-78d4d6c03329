{"date":"Sun Jun 01 2025 01:30:51 GMT-0400 (hora estándar de Chile)","error":{"address":"::1","code":"EADDRINUSE","errno":-4091,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use ::1:3001\nError: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","os":{"loadavg":[0,0,0],"uptime":136722.281},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4979788,"external":9182468,"heapTotal":322629632,"heapUsed":285517024,"rss":386584576},"pid":9296,"uid":null,"version":"v22.15.0"},"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","timestamp":"2025-06-01T05:30:51.119Z","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1939,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1996,"method":null,"native":false},{"column":7,"file":"node:net","function":"GetAddrInfoReqWrap.callback","line":2205,"method":"callback","native":false},{"column":8,"file":"node:dns","function":"GetAddrInfoReqWrap.onlookupall [as oncomplete]","line":134,"method":"onlookupall [as oncomplete]","native":false}]}
{"date":"Sun Jun 01 2025 01:51:59 GMT-0400 (hora estándar de Chile)","error":{"address":"::1","code":"EADDRINUSE","errno":-4091,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use ::1:3001\nError: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","os":{"loadavg":[0,0,0],"uptime":137990.515},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4963637,"external":9166317,"heapTotal":323678208,"heapUsed":285092952,"rss":387162112},"pid":13016,"uid":null,"version":"v22.15.0"},"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","timestamp":"2025-06-01T05:51:59.345Z","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1939,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1996,"method":null,"native":false},{"column":7,"file":"node:net","function":"GetAddrInfoReqWrap.callback","line":2205,"method":"callback","native":false},{"column":8,"file":"node:dns","function":"GetAddrInfoReqWrap.onlookupall [as oncomplete]","line":134,"method":"onlookupall [as oncomplete]","native":false}]}
{"date":"Sun Jun 01 2025 01:52:46 GMT-0400 (hora estándar de Chile)","error":{"address":"::1","code":"EADDRINUSE","errno":-4091,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use ::1:3001\nError: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","os":{"loadavg":[0,0,0],"uptime":138037.546},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4963765,"external":9166445,"heapTotal":323940352,"heapUsed":285155520,"rss":386465792},"pid":28508,"uid":null,"version":"v22.15.0"},"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","timestamp":"2025-06-01T05:52:46.381Z","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1939,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1996,"method":null,"native":false},{"column":7,"file":"node:net","function":"GetAddrInfoReqWrap.callback","line":2205,"method":"callback","native":false},{"column":8,"file":"node:dns","function":"GetAddrInfoReqWrap.onlookupall [as oncomplete]","line":134,"method":"onlookupall [as oncomplete]","native":false}]}
{"date":"Sun Jun 01 2025 13:57:56 GMT-0400 (hora estándar de Chile)","error":{"diagnosticCodes":[2339]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auth.ts\u001b[0m:\u001b[93m75\u001b[0m:\u001b[93m18\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'NODE_ENV' does not exist on type '{ readonly SERVER: { readonly PORT: number; readonly HOST: string; readonly NODE_ENV: string; }; readonly DATABASE: { readonly SUPABASE_URL: string; readonly SUPABASE_ANON_KEY: string; readonly SUPABASE_SERVICE_ROLE_KEY: string; }; ... 6 more ...; readonly APP: { ...; }; }'.\r\n\r\n\u001b[7m75\u001b[0m       if (CONFIG.NODE_ENV === 'development' && process.env.DISABLE_AUTH_IN_DEV === 'true') {\r\n\u001b[7m  \u001b[0m \u001b[91m                 ~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auth.ts\u001b[0m:\u001b[93m75\u001b[0m:\u001b[93m18\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'NODE_ENV' does not exist on type '{ readonly SERVER: { readonly PORT: number; readonly HOST: string; readonly NODE_ENV: string; }; readonly DATABASE: { readonly SUPABASE_URL: string; readonly SUPABASE_ANON_KEY: string; readonly SUPABASE_SERVICE_ROLE_KEY: string; }; ... 6 more ...; readonly APP: { ...; }; }'.\r\n\r\n\u001b[7m75\u001b[0m       if (CONFIG.NODE_ENV === 'development' && process.env.DISABLE_AUTH_IN_DEV === 'true') {\r\n\u001b[7m  \u001b[0m \u001b[91m                 ~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":181547.531},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4952841,"external":9001926,"heapTotal":267948032,"heapUsed":241896184,"rss":324415488},"pid":3128,"uid":null,"version":"v22.15.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auth.ts\u001b[0m:\u001b[93m75\u001b[0m:\u001b[93m18\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'NODE_ENV' does not exist on type '{ readonly SERVER: { readonly PORT: number; readonly HOST: string; readonly NODE_ENV: string; }; readonly DATABASE: { readonly SUPABASE_URL: string; readonly SUPABASE_ANON_KEY: string; readonly SUPABASE_SERVICE_ROLE_KEY: string; }; ... 6 more ...; readonly APP: { ...; }; }'.\r\n\r\n\u001b[7m75\u001b[0m       if (CONFIG.NODE_ENV === 'development' && process.env.DISABLE_AUTH_IN_DEV === 'true') {\r\n\u001b[7m  \u001b[0m \u001b[91m                 ~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-01T17:57:56.365Z","trace":[{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Sun Jun 01 2025 13:58:05 GMT-0400 (hora estándar de Chile)","error":{"diagnosticCodes":[2339]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auth.ts\u001b[0m:\u001b[93m75\u001b[0m:\u001b[93m18\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'NODE_ENV' does not exist on type '{ readonly SERVER: { readonly PORT: number; readonly HOST: string; readonly NODE_ENV: string; }; readonly DATABASE: { readonly SUPABASE_URL: string; readonly SUPABASE_ANON_KEY: string; readonly SUPABASE_SERVICE_ROLE_KEY: string; }; ... 6 more ...; readonly APP: { ...; }; }'.\r\n\r\n\u001b[7m75\u001b[0m       if (CONFIG.NODE_ENV === 'development' && process.env.DISABLE_AUTH_IN_DEV === 'true') {\r\n\u001b[7m  \u001b[0m \u001b[91m                 ~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auth.ts\u001b[0m:\u001b[93m75\u001b[0m:\u001b[93m18\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'NODE_ENV' does not exist on type '{ readonly SERVER: { readonly PORT: number; readonly HOST: string; readonly NODE_ENV: string; }; readonly DATABASE: { readonly SUPABASE_URL: string; readonly SUPABASE_ANON_KEY: string; readonly SUPABASE_SERVICE_ROLE_KEY: string; }; ... 6 more ...; readonly APP: { ...; }; }'.\r\n\r\n\u001b[7m75\u001b[0m       if (CONFIG.NODE_ENV === 'development' && process.env.DISABLE_AUTH_IN_DEV === 'true') {\r\n\u001b[7m  \u001b[0m \u001b[91m                 ~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":181556.593},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4952825,"external":9001910,"heapTotal":266375168,"heapUsed":244132016,"rss":323477504},"pid":26352,"uid":null,"version":"v22.15.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auth.ts\u001b[0m:\u001b[93m75\u001b[0m:\u001b[93m18\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'NODE_ENV' does not exist on type '{ readonly SERVER: { readonly PORT: number; readonly HOST: string; readonly NODE_ENV: string; }; readonly DATABASE: { readonly SUPABASE_URL: string; readonly SUPABASE_ANON_KEY: string; readonly SUPABASE_SERVICE_ROLE_KEY: string; }; ... 6 more ...; readonly APP: { ...; }; }'.\r\n\r\n\u001b[7m75\u001b[0m       if (CONFIG.NODE_ENV === 'development' && process.env.DISABLE_AUTH_IN_DEV === 'true') {\r\n\u001b[7m  \u001b[0m \u001b[91m                 ~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-01T17:58:05.427Z","trace":[{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Sun Jun 01 2025 13:58:59 GMT-0400 (hora estándar de Chile)","error":{"diagnosticCodes":[2339]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auth.ts\u001b[0m:\u001b[93m75\u001b[0m:\u001b[93m18\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'NODE_ENV' does not exist on type '{ readonly SERVER: { readonly PORT: number; readonly HOST: string; readonly NODE_ENV: string; }; readonly DATABASE: { readonly SUPABASE_URL: string; readonly SUPABASE_ANON_KEY: string; readonly SUPABASE_SERVICE_ROLE_KEY: string; }; ... 6 more ...; readonly APP: { ...; }; }'.\r\n\r\n\u001b[7m75\u001b[0m       if (CONFIG.NODE_ENV === 'development' && process.env.DISABLE_AUTH_IN_DEV === 'true') {\r\n\u001b[7m  \u001b[0m \u001b[91m                 ~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auth.ts\u001b[0m:\u001b[93m75\u001b[0m:\u001b[93m18\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'NODE_ENV' does not exist on type '{ readonly SERVER: { readonly PORT: number; readonly HOST: string; readonly NODE_ENV: string; }; readonly DATABASE: { readonly SUPABASE_URL: string; readonly SUPABASE_ANON_KEY: string; readonly SUPABASE_SERVICE_ROLE_KEY: string; }; ... 6 more ...; readonly APP: { ...; }; }'.\r\n\r\n\u001b[7m75\u001b[0m       if (CONFIG.NODE_ENV === 'development' && process.env.DISABLE_AUTH_IN_DEV === 'true') {\r\n\u001b[7m  \u001b[0m \u001b[91m                 ~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":181610.218},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4952849,"external":9001934,"heapTotal":267948032,"heapUsed":241352992,"rss":325365760},"pid":17804,"uid":null,"version":"v22.15.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/middleware/auth.ts\u001b[0m:\u001b[93m75\u001b[0m:\u001b[93m18\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2339: \u001b[0mProperty 'NODE_ENV' does not exist on type '{ readonly SERVER: { readonly PORT: number; readonly HOST: string; readonly NODE_ENV: string; }; readonly DATABASE: { readonly SUPABASE_URL: string; readonly SUPABASE_ANON_KEY: string; readonly SUPABASE_SERVICE_ROLE_KEY: string; }; ... 6 more ...; readonly APP: { ...; }; }'.\r\n\r\n\u001b[7m75\u001b[0m       if (CONFIG.NODE_ENV === 'development' && process.env.DISABLE_AUTH_IN_DEV === 'true') {\r\n\u001b[7m  \u001b[0m \u001b[91m                 ~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-01T17:58:59.056Z","trace":[{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Sun Jun 01 2025 16:27:05 GMT-0400 (hora estándar de Chile)","error":{"diagnosticCodes":[2304]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/controllers/alerts.ts\u001b[0m:\u001b[93m236\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationService'.\r\n\r\n\u001b[7m236\u001b[0m         await notificationService.sendVolcanoAlert(newAlert);\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/controllers/alerts.ts\u001b[0m:\u001b[93m236\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationService'.\r\n\r\n\u001b[7m236\u001b[0m         await notificationService.sendVolcanoAlert(newAlert);\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":190497},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4942850,"external":8991935,"heapTotal":290172928,"heapUsed":261191256,"rss":347176960},"pid":1836,"uid":null,"version":"v22.15.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/controllers/alerts.ts\u001b[0m:\u001b[93m236\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationService'.\r\n\r\n\u001b[7m236\u001b[0m         await notificationService.sendVolcanoAlert(newAlert);\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-01T20:27:05.831Z","trace":[{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Sun Jun 01 2025 16:27:18 GMT-0400 (hora estándar de Chile)","error":{"diagnosticCodes":[2304,2304]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/controllers/alerts.ts\u001b[0m:\u001b[93m236\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationService'.\r\n\r\n\u001b[7m236\u001b[0m         await notificationService.sendVolcanoAlert(newAlert);\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/controllers/alerts.ts\u001b[0m:\u001b[93m341\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationService'.\r\n\r\n\u001b[7m341\u001b[0m         await notificationService.sendVolcanoAlert(updatedAlert);\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/controllers/alerts.ts\u001b[0m:\u001b[93m236\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationService'.\r\n\r\n\u001b[7m236\u001b[0m         await notificationService.sendVolcanoAlert(newAlert);\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/controllers/alerts.ts\u001b[0m:\u001b[93m341\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationService'.\r\n\r\n\u001b[7m341\u001b[0m         await notificationService.sendVolcanoAlert(updatedAlert);\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":190509.468},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4942842,"external":8991927,"heapTotal":290697216,"heapUsed":262152048,"rss":347615232},"pid":38808,"uid":null,"version":"v22.15.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/controllers/alerts.ts\u001b[0m:\u001b[93m236\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationService'.\r\n\r\n\u001b[7m236\u001b[0m         await notificationService.sendVolcanoAlert(newAlert);\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/controllers/alerts.ts\u001b[0m:\u001b[93m341\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationService'.\r\n\r\n\u001b[7m341\u001b[0m         await notificationService.sendVolcanoAlert(updatedAlert);\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-01T20:27:18.296Z","trace":[{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Sun Jun 01 2025 16:27:43 GMT-0400 (hora estándar de Chile)","error":{"diagnosticCodes":[2304,2304]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/controllers/alerts.ts\u001b[0m:\u001b[93m236\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationService'.\r\n\r\n\u001b[7m236\u001b[0m         await notificationService.sendVolcanoAlert(newAlert);\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/controllers/alerts.ts\u001b[0m:\u001b[93m341\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationService'.\r\n\r\n\u001b[7m341\u001b[0m         await notificationService.sendVolcanoAlert(updatedAlert);\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/controllers/alerts.ts\u001b[0m:\u001b[93m236\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationService'.\r\n\r\n\u001b[7m236\u001b[0m         await notificationService.sendVolcanoAlert(newAlert);\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/controllers/alerts.ts\u001b[0m:\u001b[93m341\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationService'.\r\n\r\n\u001b[7m341\u001b[0m         await notificationService.sendVolcanoAlert(updatedAlert);\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":190534.984},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4942794,"external":8991879,"heapTotal":289910784,"heapUsed":262321528,"rss":347189248},"pid":14020,"uid":null,"version":"v22.15.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/controllers/alerts.ts\u001b[0m:\u001b[93m236\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationService'.\r\n\r\n\u001b[7m236\u001b[0m         await notificationService.sendVolcanoAlert(newAlert);\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/controllers/alerts.ts\u001b[0m:\u001b[93m341\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationService'.\r\n\r\n\u001b[7m341\u001b[0m         await notificationService.sendVolcanoAlert(updatedAlert);\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-01T20:27:43.810Z","trace":[{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Sun Jun 01 2025 16:27:50 GMT-0400 (hora estándar de Chile)","error":{"diagnosticCodes":[2304,2304]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/controllers/alerts.ts\u001b[0m:\u001b[93m236\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationService'.\r\n\r\n\u001b[7m236\u001b[0m         await notificationService.sendVolcanoAlert(newAlert);\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/controllers/alerts.ts\u001b[0m:\u001b[93m341\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationService'.\r\n\r\n\u001b[7m341\u001b[0m         await notificationService.sendVolcanoAlert(updatedAlert);\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/controllers/alerts.ts\u001b[0m:\u001b[93m236\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationService'.\r\n\r\n\u001b[7m236\u001b[0m         await notificationService.sendVolcanoAlert(newAlert);\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/controllers/alerts.ts\u001b[0m:\u001b[93m341\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationService'.\r\n\r\n\u001b[7m341\u001b[0m         await notificationService.sendVolcanoAlert(updatedAlert);\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":190541.562},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4942874,"external":8991959,"heapTotal":285454336,"heapUsed":253921096,"rss":344739840},"pid":13388,"uid":null,"version":"v22.15.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/controllers/alerts.ts\u001b[0m:\u001b[93m236\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationService'.\r\n\r\n\u001b[7m236\u001b[0m         await notificationService.sendVolcanoAlert(newAlert);\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/controllers/alerts.ts\u001b[0m:\u001b[93m341\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationService'.\r\n\r\n\u001b[7m341\u001b[0m         await notificationService.sendVolcanoAlert(updatedAlert);\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-01T20:27:50.407Z","trace":[{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Sun Jun 01 2025 16:28:00 GMT-0400 (hora estándar de Chile)","error":{"diagnosticCodes":[2304,2304]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/controllers/alerts.ts\u001b[0m:\u001b[93m236\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationService'.\r\n\r\n\u001b[7m236\u001b[0m         await notificationService.sendVolcanoAlert(newAlert);\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/controllers/alerts.ts\u001b[0m:\u001b[93m341\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationService'.\r\n\r\n\u001b[7m341\u001b[0m         await notificationService.sendVolcanoAlert(updatedAlert);\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/controllers/alerts.ts\u001b[0m:\u001b[93m236\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationService'.\r\n\r\n\u001b[7m236\u001b[0m         await notificationService.sendVolcanoAlert(newAlert);\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/controllers/alerts.ts\u001b[0m:\u001b[93m341\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationService'.\r\n\r\n\u001b[7m341\u001b[0m         await notificationService.sendVolcanoAlert(updatedAlert);\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":190551.546},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4942954,"external":8992039,"heapTotal":284667904,"heapUsed":254009168,"rss":347807744},"pid":30920,"uid":null,"version":"v22.15.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/controllers/alerts.ts\u001b[0m:\u001b[93m236\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationService'.\r\n\r\n\u001b[7m236\u001b[0m         await notificationService.sendVolcanoAlert(newAlert);\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\u001b[96msrc/controllers/alerts.ts\u001b[0m:\u001b[93m341\u001b[0m:\u001b[93m15\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationService'.\r\n\r\n\u001b[7m341\u001b[0m         await notificationService.sendVolcanoAlert(updatedAlert);\r\n\u001b[7m   \u001b[0m \u001b[91m              ~~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-01T20:28:00.382Z","trace":[{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Sun Jun 01 2025 16:28:10 GMT-0400 (hora estándar de Chile)","error":{"diagnosticCodes":[2304]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":190561.921},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4942858,"external":8991943,"heapTotal":266113024,"heapUsed":236917224,"rss":327172096},"pid":35932,"uid":null,"version":"v22.15.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-01T20:28:10.774Z","trace":[{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Sun Jun 01 2025 16:33:52 GMT-0400 (hora estándar de Chile)","error":{"diagnosticCodes":[2304]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":190903.5},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4942898,"external":8991983,"heapTotal":267685888,"heapUsed":233967328,"rss":330735616},"pid":11556,"uid":null,"version":"v22.15.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-01T20:33:52.330Z","trace":[{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Sun Jun 01 2025 16:34:13 GMT-0400 (hora estándar de Chile)","error":{"diagnosticCodes":[2304]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":190924.39},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4942978,"external":8992063,"heapTotal":267423744,"heapUsed":234492864,"rss":330215424},"pid":35888,"uid":null,"version":"v22.15.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-01T20:34:13.229Z","trace":[{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Sun Jun 01 2025 16:50:13 GMT-0400 (hora estándar de Chile)","error":{"diagnosticCodes":[2304]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":191884.781},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4942946,"external":8992031,"heapTotal":266375168,"heapUsed":236697896,"rss":327524352},"pid":31472,"uid":null,"version":"v22.15.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-01T20:50:13.618Z","trace":[{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Sun Jun 01 2025 16:53:05 GMT-0400 (hora estándar de Chile)","error":{"diagnosticCodes":[2304]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":192056.781},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4942954,"external":8992039,"heapTotal":269783040,"heapUsed":232439072,"rss":334098432},"pid":32636,"uid":null,"version":"v22.15.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-01T20:53:05.617Z","trace":[{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Sun Jun 01 2025 16:53:43 GMT-0400 (hora estándar de Chile)","error":{"diagnosticCodes":[2304]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":192094.312},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4942986,"external":8992071,"heapTotal":266375168,"heapUsed":236467800,"rss":326623232},"pid":31360,"uid":null,"version":"v22.15.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-01T20:53:43.151Z","trace":[{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Sun Jun 01 2025 16:53:43 GMT-0400 (hora estándar de Chile)","error":{"diagnosticCodes":[2304]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":192094.421},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4943002,"external":8992087,"heapTotal":267685888,"heapUsed":234714448,"rss":329617408},"pid":28044,"uid":null,"version":"v22.15.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-01T20:53:43.261Z","trace":[{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Sun Jun 01 2025 16:54:05 GMT-0400 (hora estándar de Chile)","error":{"diagnosticCodes":[2304]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":192116.281},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4942962,"external":8992047,"heapTotal":267685888,"heapUsed":234957624,"rss":324243456},"pid":9544,"uid":null,"version":"v22.15.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-01T20:54:05.122Z","trace":[{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Sun Jun 01 2025 16:54:05 GMT-0400 (hora estándar de Chile)","error":{"diagnosticCodes":[2304]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":192116.312},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4942946,"external":9008239,"heapTotal":268476416,"heapUsed":245075536,"rss":321101824},"pid":8880,"uid":null,"version":"v22.15.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-01T20:54:05.151Z","trace":[{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Sun Jun 01 2025 16:54:25 GMT-0400 (hora estándar de Chile)","error":{"diagnosticCodes":[2304]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":192136.312},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4942930,"external":8992015,"heapTotal":265588736,"heapUsed":237050632,"rss":328142848},"pid":28244,"uid":null,"version":"v22.15.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-01T20:54:25.151Z","trace":[{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Sun Jun 01 2025 16:54:25 GMT-0400 (hora estándar de Chile)","error":{"diagnosticCodes":[2304]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":192136.359},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4943026,"external":8992111,"heapTotal":266637312,"heapUsed":236927608,"rss":328130560},"pid":26816,"uid":null,"version":"v22.15.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-01T20:54:25.198Z","trace":[{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Sun Jun 01 2025 16:55:36 GMT-0400 (hora estándar de Chile)","error":{"diagnosticCodes":[2304]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":192207.843},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4943058,"external":8992143,"heapTotal":267685888,"heapUsed":236496208,"rss":328134656},"pid":29124,"uid":null,"version":"v22.15.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-01T20:55:36.671Z","trace":[{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Sun Jun 01 2025 16:55:36 GMT-0400 (hora estándar de Chile)","error":{"diagnosticCodes":[2304]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":192207.859},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4943042,"external":8992127,"heapTotal":272928768,"heapUsed":245995144,"rss":336089088},"pid":17592,"uid":null,"version":"v22.15.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/routes/index.ts\u001b[0m:\u001b[93m104\u001b[0m:\u001b[93m51\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2304: \u001b[0mCannot find name 'notificationRoutes'.\r\n\r\n\u001b[7m104\u001b[0m router.use('/notifications', authenticateToken(), notificationRoutes);\r\n\u001b[7m   \u001b[0m \u001b[91m                                                  ~~~~~~~~~~~~~~~~~~\u001b[0m\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-01T20:55:36.702Z","trace":[{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Sun Jun 01 2025 16:56:00 GMT-0400 (hora estándar de Chile)","error":{"diagnosticCodes":[2345]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/services/notifications.ts\u001b[0m:\u001b[93m172\u001b[0m:\u001b[93m44\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2345: \u001b[0mArgument of type '{ type: string; alertId: any; alertLevel: any; volcanoName: any; volcanoLat: any; volcanoLng: any; title: string; message: any; timestamp: string; vibrationPattern: never[] | number[]; sound: boolean; vibration: boolean; priority: \"high\" | \"normal\"; }' is not assignable to parameter of type 'PushNotificationPayload'.\r\n  Property 'body' is missing in type '{ type: string; alertId: any; alertLevel: any; volcanoName: any; volcanoLat: any; volcanoLng: any; title: string; message: any; timestamp: string; vibrationPattern: never[] | number[]; sound: boolean; vibration: boolean; priority: \"high\" | \"normal\"; }' but required in type 'PushNotificationPayload'.\r\n\r\n\u001b[7m172\u001b[0m       await this.logNotification(alert.id, notificationData, target, {\r\n\u001b[7m   \u001b[0m \u001b[91m                                           ~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/services/notifications.ts\u001b[0m:\u001b[93m15\u001b[0m:\u001b[93m3\u001b[0m\r\n    \u001b[7m15\u001b[0m   body: string;\r\n    \u001b[7m  \u001b[0m \u001b[96m  ~~~~\u001b[0m\r\n    'body' is declared here.\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/services/notifications.ts\u001b[0m:\u001b[93m172\u001b[0m:\u001b[93m44\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2345: \u001b[0mArgument of type '{ type: string; alertId: any; alertLevel: any; volcanoName: any; volcanoLat: any; volcanoLng: any; title: string; message: any; timestamp: string; vibrationPattern: never[] | number[]; sound: boolean; vibration: boolean; priority: \"high\" | \"normal\"; }' is not assignable to parameter of type 'PushNotificationPayload'.\r\n  Property 'body' is missing in type '{ type: string; alertId: any; alertLevel: any; volcanoName: any; volcanoLat: any; volcanoLng: any; title: string; message: any; timestamp: string; vibrationPattern: never[] | number[]; sound: boolean; vibration: boolean; priority: \"high\" | \"normal\"; }' but required in type 'PushNotificationPayload'.\r\n\r\n\u001b[7m172\u001b[0m       await this.logNotification(alert.id, notificationData, target, {\r\n\u001b[7m   \u001b[0m \u001b[91m                                           ~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/services/notifications.ts\u001b[0m:\u001b[93m15\u001b[0m:\u001b[93m3\u001b[0m\r\n    \u001b[7m15\u001b[0m   body: string;\r\n    \u001b[7m  \u001b[0m \u001b[96m  ~~~~\u001b[0m\r\n    'body' is declared here.\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":192232.109},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4942930,"external":9015058,"heapTotal":289386496,"heapUsed":252219416,"rss":347045888},"pid":25048,"uid":null,"version":"v22.15.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/services/notifications.ts\u001b[0m:\u001b[93m172\u001b[0m:\u001b[93m44\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2345: \u001b[0mArgument of type '{ type: string; alertId: any; alertLevel: any; volcanoName: any; volcanoLat: any; volcanoLng: any; title: string; message: any; timestamp: string; vibrationPattern: never[] | number[]; sound: boolean; vibration: boolean; priority: \"high\" | \"normal\"; }' is not assignable to parameter of type 'PushNotificationPayload'.\r\n  Property 'body' is missing in type '{ type: string; alertId: any; alertLevel: any; volcanoName: any; volcanoLat: any; volcanoLng: any; title: string; message: any; timestamp: string; vibrationPattern: never[] | number[]; sound: boolean; vibration: boolean; priority: \"high\" | \"normal\"; }' but required in type 'PushNotificationPayload'.\r\n\r\n\u001b[7m172\u001b[0m       await this.logNotification(alert.id, notificationData, target, {\r\n\u001b[7m   \u001b[0m \u001b[91m                                           ~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/services/notifications.ts\u001b[0m:\u001b[93m15\u001b[0m:\u001b[93m3\u001b[0m\r\n    \u001b[7m15\u001b[0m   body: string;\r\n    \u001b[7m  \u001b[0m \u001b[96m  ~~~~\u001b[0m\r\n    'body' is declared here.\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-01T20:56:00.940Z","trace":[{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Sun Jun 01 2025 16:56:00 GMT-0400 (hora estándar de Chile)","error":{"diagnosticCodes":[2345]},"exception":true,"level":"error","message":"uncaughtException: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/services/notifications.ts\u001b[0m:\u001b[93m172\u001b[0m:\u001b[93m44\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2345: \u001b[0mArgument of type '{ type: string; alertId: any; alertLevel: any; volcanoName: any; volcanoLat: any; volcanoLng: any; title: string; message: any; timestamp: string; vibrationPattern: never[] | number[]; sound: boolean; vibration: boolean; priority: \"high\" | \"normal\"; }' is not assignable to parameter of type 'PushNotificationPayload'.\r\n  Property 'body' is missing in type '{ type: string; alertId: any; alertLevel: any; volcanoName: any; volcanoLat: any; volcanoLng: any; title: string; message: any; timestamp: string; vibrationPattern: never[] | number[]; sound: boolean; vibration: boolean; priority: \"high\" | \"normal\"; }' but required in type 'PushNotificationPayload'.\r\n\r\n\u001b[7m172\u001b[0m       await this.logNotification(alert.id, notificationData, target, {\r\n\u001b[7m   \u001b[0m \u001b[91m                                           ~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/services/notifications.ts\u001b[0m:\u001b[93m15\u001b[0m:\u001b[93m3\u001b[0m\r\n    \u001b[7m15\u001b[0m   body: string;\r\n    \u001b[7m  \u001b[0m \u001b[96m  ~~~~\u001b[0m\r\n    'body' is declared here.\r\n\nTSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/services/notifications.ts\u001b[0m:\u001b[93m172\u001b[0m:\u001b[93m44\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2345: \u001b[0mArgument of type '{ type: string; alertId: any; alertLevel: any; volcanoName: any; volcanoLat: any; volcanoLng: any; title: string; message: any; timestamp: string; vibrationPattern: never[] | number[]; sound: boolean; vibration: boolean; priority: \"high\" | \"normal\"; }' is not assignable to parameter of type 'PushNotificationPayload'.\r\n  Property 'body' is missing in type '{ type: string; alertId: any; alertLevel: any; volcanoName: any; volcanoLat: any; volcanoLng: any; title: string; message: any; timestamp: string; vibrationPattern: never[] | number[]; sound: boolean; vibration: boolean; priority: \"high\" | \"normal\"; }' but required in type 'PushNotificationPayload'.\r\n\r\n\u001b[7m172\u001b[0m       await this.logNotification(alert.id, notificationData, target, {\r\n\u001b[7m   \u001b[0m \u001b[91m                                           ~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/services/notifications.ts\u001b[0m:\u001b[93m15\u001b[0m:\u001b[93m3\u001b[0m\r\n    \u001b[7m15\u001b[0m   body: string;\r\n    \u001b[7m  \u001b[0m \u001b[96m  ~~~~\u001b[0m\r\n    'body' is declared here.\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":192232.125},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4966029,"external":9015114,"heapTotal":289124352,"heapUsed":259321800,"rss":347275264},"pid":29036,"uid":null,"version":"v22.15.0"},"stack":"TSError: ⨯ Unable to compile TypeScript:\n\u001b[96msrc/services/notifications.ts\u001b[0m:\u001b[93m172\u001b[0m:\u001b[93m44\u001b[0m - \u001b[91merror\u001b[0m\u001b[90m TS2345: \u001b[0mArgument of type '{ type: string; alertId: any; alertLevel: any; volcanoName: any; volcanoLat: any; volcanoLng: any; title: string; message: any; timestamp: string; vibrationPattern: never[] | number[]; sound: boolean; vibration: boolean; priority: \"high\" | \"normal\"; }' is not assignable to parameter of type 'PushNotificationPayload'.\r\n  Property 'body' is missing in type '{ type: string; alertId: any; alertLevel: any; volcanoName: any; volcanoLat: any; volcanoLng: any; title: string; message: any; timestamp: string; vibrationPattern: never[] | number[]; sound: boolean; vibration: boolean; priority: \"high\" | \"normal\"; }' but required in type 'PushNotificationPayload'.\r\n\r\n\u001b[7m172\u001b[0m       await this.logNotification(alert.id, notificationData, target, {\r\n\u001b[7m   \u001b[0m \u001b[91m                                           ~~~~~~~~~~~~~~~~\u001b[0m\r\n\r\n  \u001b[96msrc/services/notifications.ts\u001b[0m:\u001b[93m15\u001b[0m:\u001b[93m3\u001b[0m\r\n    \u001b[7m15\u001b[0m   body: string;\r\n    \u001b[7m  \u001b[0m \u001b[96m  ~~~~\u001b[0m\r\n    'body' is declared here.\r\n\n    at createTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:859:12)\n    at reportTSError (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:863:19)\n    at getOutput (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1077:36)\n    at Object.compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1433:41)\n    at Module.m._compile (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1617:30)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-06-01T20:56:00.954Z","trace":[{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"createTSError","line":859,"method":null,"native":false},{"column":19,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"reportTSError","line":863,"method":null,"native":false},{"column":36,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"getOutput","line":1077,"method":null,"native":false},{"column":41,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.compile","line":1433,"method":"compile","native":false},{"column":30,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1617,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":null,"line":1895,"method":null,"native":false},{"column":12,"file":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1465,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Function._load","line":1282,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Sun Jun 01 2025 16:56:23 GMT-0400 (hora estándar de Chile)","error":{"address":"0.0.0.0","code":"EADDRINUSE","errno":-4091,"port":3002,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use 0.0.0.0:3002\nError: listen EADDRINUSE: address already in use 0.0.0.0:3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","os":{"loadavg":[0,0,0],"uptime":192254.468},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4984520,"external":9187200,"heapTotal":341483520,"heapUsed":311505536,"rss":407719936},"pid":2184,"uid":null,"version":"v22.15.0"},"stack":"Error: listen EADDRINUSE: address already in use 0.0.0.0:3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-01T20:56:23.303Z","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1939,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1996,"method":null,"native":false},{"column":7,"file":"node:net","function":null,"line":2205,"method":null,"native":false},{"column":21,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":90,"method":null,"native":false}]}
{"date":"Sun Jun 01 2025 16:56:39 GMT-0400 (hora estándar de Chile)","error":{"address":"0.0.0.0","code":"EADDRINUSE","errno":-4091,"port":3002,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use 0.0.0.0:3002\nError: listen EADDRINUSE: address already in use 0.0.0.0:3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","os":{"loadavg":[0,0,0],"uptime":192270.39},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4984429,"external":9187109,"heapTotal":341221376,"heapUsed":313117136,"rss":407363584},"pid":22016,"uid":null,"version":"v22.15.0"},"stack":"Error: listen EADDRINUSE: address already in use 0.0.0.0:3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-01T20:56:39.218Z","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1939,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1996,"method":null,"native":false},{"column":7,"file":"node:net","function":null,"line":2205,"method":null,"native":false},{"column":21,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":90,"method":null,"native":false}]}
{"date":"Sun Jun 01 2025 16:57:55 GMT-0400 (hora estándar de Chile)","error":{"address":"0.0.0.0","code":"EADDRINUSE","errno":-4091,"port":3002,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use 0.0.0.0:3002\nError: listen EADDRINUSE: address already in use 0.0.0.0:3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","os":{"loadavg":[0,0,0],"uptime":192347.046},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4963686,"external":9166366,"heapTotal":331259904,"heapUsed":294244336,"rss":397565952},"pid":15132,"uid":null,"version":"v22.15.0"},"stack":"Error: listen EADDRINUSE: address already in use 0.0.0.0:3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-01T20:57:55.880Z","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1939,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1996,"method":null,"native":false},{"column":7,"file":"node:net","function":null,"line":2205,"method":null,"native":false},{"column":21,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":90,"method":null,"native":false}]}
{"date":"Sun Jun 01 2025 16:58:30 GMT-0400 (hora estándar de Chile)","error":{"address":"0.0.0.0","code":"EADDRINUSE","errno":-4091,"port":3002,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use 0.0.0.0:3002\nError: listen EADDRINUSE: address already in use 0.0.0.0:3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","os":{"loadavg":[0,0,0],"uptime":192381.593},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4963574,"external":9166254,"heapTotal":330997760,"heapUsed":293863232,"rss":395698176},"pid":37352,"uid":null,"version":"v22.15.0"},"stack":"Error: listen EADDRINUSE: address already in use 0.0.0.0:3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-01T20:58:30.430Z","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1939,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1996,"method":null,"native":false},{"column":7,"file":"node:net","function":null,"line":2205,"method":null,"native":false},{"column":21,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":90,"method":null,"native":false}]}
{"date":"Sun Jun 01 2025 16:59:13 GMT-0400 (hora estándar de Chile)","error":{"address":"0.0.0.0","code":"EADDRINUSE","errno":-4091,"port":3002,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use 0.0.0.0:3002\nError: listen EADDRINUSE: address already in use 0.0.0.0:3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","os":{"loadavg":[0,0,0],"uptime":192425.046},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4963575,"external":9166255,"heapTotal":330735616,"heapUsed":293869288,"rss":396570624},"pid":3840,"uid":null,"version":"v22.15.0"},"stack":"Error: listen EADDRINUSE: address already in use 0.0.0.0:3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-01T20:59:13.888Z","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1939,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1996,"method":null,"native":false},{"column":7,"file":"node:net","function":null,"line":2205,"method":null,"native":false},{"column":21,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":90,"method":null,"native":false}]}
{"date":"Sun Jun 01 2025 17:05:41 GMT-0400 (hora estándar de Chile)","error":{"address":"0.0.0.0","code":"EADDRINUSE","errno":-4091,"port":3002,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use 0.0.0.0:3002\nError: listen EADDRINUSE: address already in use 0.0.0.0:3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","os":{"loadavg":[0,0,0],"uptime":192812.515},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4963566,"external":9166246,"heapTotal":330735616,"heapUsed":294037312,"rss":397570048},"pid":20572,"uid":null,"version":"v22.15.0"},"stack":"Error: listen EADDRINUSE: address already in use 0.0.0.0:3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-01T21:05:41.350Z","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1939,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1996,"method":null,"native":false},{"column":7,"file":"node:net","function":null,"line":2205,"method":null,"native":false},{"column":21,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":90,"method":null,"native":false}]}
{"date":"Sun Jun 01 2025 18:09:41 GMT-0400 (hora estándar de Chile)","error":{"address":"0.0.0.0","code":"EADDRINUSE","errno":-4091,"port":3002,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use 0.0.0.0:3002\nError: listen EADDRINUSE: address already in use 0.0.0.0:3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","os":{"loadavg":[0,0,0],"uptime":196652.5},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4963573,"external":9166253,"heapTotal":331259904,"heapUsed":294004864,"rss":395845632},"pid":35388,"uid":null,"version":"v22.15.0"},"stack":"Error: listen EADDRINUSE: address already in use 0.0.0.0:3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-01T22:09:41.329Z","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1939,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1996,"method":null,"native":false},{"column":7,"file":"node:net","function":null,"line":2205,"method":null,"native":false},{"column":21,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":90,"method":null,"native":false}]}
{"date":"Mon Jun 02 2025 09:24:00 GMT-0400 (hora estándar de Chile)","error":{"address":"0.0.0.0","code":"EADDRINUSE","errno":-4091,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use 0.0.0.0:3001\nError: listen EADDRINUSE: address already in use 0.0.0.0:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","os":{"loadavg":[0,0,0],"uptime":251512.015},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4963669,"external":9166349,"heapTotal":330207232,"heapUsed":293771072,"rss":391733248},"pid":31180,"uid":null,"version":"v22.15.0"},"stack":"Error: listen EADDRINUSE: address already in use 0.0.0.0:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-02T13:24:00.854Z","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1939,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1996,"method":null,"native":false},{"column":7,"file":"node:net","function":null,"line":2205,"method":null,"native":false},{"column":21,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":90,"method":null,"native":false}]}
{"date":"Mon Jun 02 2025 10:37:39 GMT-0400 (hora estándar de Chile)","error":{"address":"0.0.0.0","code":"EADDRINUSE","errno":-4091,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use 0.0.0.0:3001\nError: listen EADDRINUSE: address already in use 0.0.0.0:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","os":{"loadavg":[0,0,0],"uptime":255930.625},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4963629,"external":9166309,"heapTotal":330207232,"heapUsed":293793304,"rss":390017024},"pid":14080,"uid":null,"version":"v22.15.0"},"stack":"Error: listen EADDRINUSE: address already in use 0.0.0.0:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-02T14:37:39.460Z","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1939,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1996,"method":null,"native":false},{"column":7,"file":"node:net","function":null,"line":2205,"method":null,"native":false},{"column":21,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":90,"method":null,"native":false}]}
{"date":"Mon Jun 02 2025 14:36:01 GMT-0400 (hora estándar de Chile)","error":{"address":"0.0.0.0","code":"EADDRINUSE","errno":-4091,"port":3001,"syscall":"listen"},"exception":true,"level":"error","message":"uncaughtException: listen EADDRINUSE: address already in use 0.0.0.0:3001\nError: listen EADDRINUSE: address already in use 0.0.0.0:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","os":{"loadavg":[0,0,0],"uptime":270232.468},"process":{"argv":["C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\node_modules\\ts-node\\dist\\bin.js","C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend\\src\\index.ts"],"cwd":"C:\\Users\\<USER>\\Documents\\augment-projects\\volcanoApp\\volcanoApp\\backoffice\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":4981773,"external":9184453,"heapTotal":342007808,"heapUsed":314174240,"rss":405098496},"pid":7440,"uid":null,"version":"v22.15.0"},"stack":"Error: listen EADDRINUSE: address already in use 0.0.0.0:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at node:net:2205:7\n    at processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-06-02T18:36:01.308Z","trace":[{"column":16,"file":"node:net","function":"Server.setupListenHandle [as _listen2]","line":1939,"method":"setupListenHandle [as _listen2]","native":false},{"column":12,"file":"node:net","function":"listenInCluster","line":1996,"method":null,"native":false},{"column":7,"file":"node:net","function":null,"line":2205,"method":null,"native":false},{"column":21,"file":"node:internal/process/task_queues","function":"processTicksAndRejections","line":90,"method":null,"native":false}]}
