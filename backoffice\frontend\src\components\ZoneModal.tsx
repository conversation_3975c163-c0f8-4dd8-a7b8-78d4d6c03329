/**
 * 🌋 Volcano App Backoffice - Modal de Zonas
 * Modal para crear y editar zonas de seguridad
 */

import { useToast } from '@/hooks/use-toast';
import { Save, Shield, X } from 'lucide-react';
import React, { useEffect, useState } from 'react';

interface Zone {
  id: string;
  name: string;
  description: string;
  zone_type: 'SAFE' | 'EMERGENCY' | 'DANGER' | 'EVACUATION' | 'RESTRICTED';
  geometry: {
    type: 'Polygon';
    coordinates: number[][][];
  };
  capacity?: number;
  contact_info?: any;
  facilities?: string[];
  is_active: boolean;
  version: number;
  created_at: string;
  updated_at: string;
  created_by?: {
    full_name: string;
    email: string;
  };
}

interface ZoneFormData {
  name: string;
  description: string;
  zone_type: string;
  capacity?: number;
  is_active: boolean;
}

interface ZoneModalProps {
  isOpen: boolean;
  onClose: () => void;
  zone?: Zone | null;
  isCreating: boolean;
  onZoneCreated: (zone: Zone) => void;
  onZoneUpdated: (zone: Zone) => void;
}

export function ZoneModal({ isOpen, onClose, zone, isCreating, onZoneCreated, onZoneUpdated }: ZoneModalProps) {
  const { toast } = useToast();
  const [formData, setFormData] = useState<ZoneFormData>({
    name: '',
    description: '',
    zone_type: 'SAFE',
    capacity: undefined,
    is_active: true
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (zone && !isCreating) {
      setFormData({
        name: zone.name,
        description: zone.description,
        zone_type: zone.zone_type,
        capacity: zone.capacity,
        is_active: zone.is_active
      });
    } else if (isCreating) {
      // Reset form for new zone
      setFormData({
        name: '',
        description: '',
        zone_type: 'SAFE',
        capacity: undefined,
        is_active: true
      });
      setErrors({});
    }
  }, [zone, isCreating]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'El nombre es requerido';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'La descripción es requerida';
    }

    if (formData.capacity !== undefined && (isNaN(formData.capacity) || formData.capacity < 0)) {
      newErrors.capacity = 'La capacidad debe ser un número positivo';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      if (isCreating) {
        // Crear nueva zona
        const defaultGeometry = {
          type: "Polygon",
          coordinates: [[
            [-71.95, -39.43],
            [-71.94, -39.43],
            [-71.94, -39.42],
            [-71.95, -39.42],
            [-71.95, -39.43]
          ]]
        };

        const zoneData = {
          ...formData,
          geometry: defaultGeometry
        };

        const response = await fetch('/api/zones', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(zoneData),
        });

        const data = await response.json();

        if (data.success) {
          toast({
            title: 'Éxito',
            description: 'Zona creada correctamente',
          });
          onZoneCreated(data.data);
        } else {
          throw new Error(data.error || 'Error al crear la zona');
        }
      } else if (zone) {
        // Actualizar zona existente
        const response = await fetch(`/api/zones/${zone.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(formData),
        });

        const data = await response.json();

        if (data.success) {
          toast({
            title: 'Éxito',
            description: 'Zona actualizada correctamente',
          });
          onZoneUpdated(data.data);
        } else {
          throw new Error(data.error || 'Error al actualizar la zona');
        }
      }
    } catch (error) {
      console.error('Error saving zone:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Error al guardar la zona',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (field: keyof ZoneFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const getZoneTypeInfo = (type: string) => {
    switch (type) {
      case 'SAFE':
        return { icon: '🛡️', color: 'text-green-600', description: 'Zona segura para refugio' };
      case 'EMERGENCY':
        return { icon: '🏥', color: 'text-blue-600', description: 'Centro de atención de emergencias' };
      case 'EVACUATION':
        return { icon: '🚨', color: 'text-yellow-600', description: 'Ruta o punto de evacuación' };
      case 'DANGER':
        return { icon: '⚠️', color: 'text-red-600', description: 'Zona de peligro volcánico' };
      default:
        return { icon: '📍', color: 'text-gray-600', description: 'Zona general' };
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <Shield className="h-6 w-6 text-blue-500" />
            <h3 className="text-lg font-medium text-gray-900">
              {isCreating ? 'Nueva Zona de Seguridad' : 'Editar Zona'}
            </h3>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
            disabled={isSubmitting}
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nombre de la Zona *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                errors.name ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Ej: Centro de Evacuación Pucón"
              disabled={isSubmitting}
            />
            {errors.name && <p className="mt-1 text-sm text-red-600">{errors.name}</p>}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Descripción *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              rows={3}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                errors.description ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Describe las características y servicios disponibles en esta zona..."
              disabled={isSubmitting}
            />
            {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
          </div>

          {/* Zone Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tipo de Zona *
            </label>
            <select
              value={formData.zone_type}
              onChange={(e) => handleChange('zone_type', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              disabled={isSubmitting}
            >
              <option value="SAFE">🛡️ Zona Segura - Refugio y protección</option>
              <option value="EMERGENCY">🏥 Emergencia - Centro de atención médica</option>
              <option value="EVACUATION">🚨 Evacuación - Ruta o punto de salida</option>
              <option value="DANGER">⚠️ Peligro - Zona de riesgo volcánico</option>
            </select>
            
            {/* Zone Type Info */}
            <div className="mt-2 p-3 bg-gray-50 rounded-md">
              <div className="flex items-center space-x-2">
                <span className="text-lg">{getZoneTypeInfo(formData.zone_type).icon}</span>
                <span className={`text-sm font-medium ${getZoneTypeInfo(formData.zone_type).color}`}>
                  {getZoneTypeInfo(formData.zone_type).description}
                </span>
              </div>
            </div>
          </div>

          {/* Capacity */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Capacidad (Opcional)
            </label>
            <input
              type="number"
              min="0"
              value={formData.capacity || ''}
              onChange={(e) => handleChange('capacity', e.target.value ? parseInt(e.target.value) : undefined)}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                errors.capacity ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Número máximo de personas"
              disabled={isSubmitting}
            />
            {errors.capacity && <p className="mt-1 text-sm text-red-600">{errors.capacity}</p>}
            <p className="mt-1 text-sm text-gray-500">
              Deja vacío si no aplica o no se conoce la capacidad
            </p>
          </div>

          {/* Active Status */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="is_active"
              checked={formData.is_active}
              onChange={(e) => handleChange('is_active', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              disabled={isSubmitting}
            />
            <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
              Zona activa (visible en el mapa y para usuarios)
            </label>
          </div>

          {/* Geometry Info */}
          {!isCreating && zone && (
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <h4 className="text-sm font-medium text-blue-900 mb-2">Información Geográfica</h4>
              <p className="text-sm text-blue-700">
                Esta zona tiene geometría definida en el mapa. Para modificar la forma o ubicación,
                usa la herramienta de edición en la pestaña "Mapa".
              </p>
            </div>
          )}

          {isCreating && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
              <h4 className="text-sm font-medium text-yellow-900 mb-2">Ubicación en el Mapa</h4>
              <p className="text-sm text-yellow-700">
                Se creará una zona básica que podrás editar posteriormente en la pestaña "Mapa"
                para definir la forma y ubicación exacta.
              </p>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              disabled={isSubmitting}
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="flex items-center space-x-2 px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
            >
              <Save className="h-4 w-4" />
              <span>{isSubmitting ? 'Guardando...' : (isCreating ? 'Crear Zona' : 'Actualizar')}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
