/**
 * 🌋 Volcano App Backoffice - Mapa Interactivo de Zonas Completo
 * Componente que integra el mapa de dibujo con estadísticas y controles avanzados
 */

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ZoneGeometry } from '@/services/geometry';
import {
    BarChart3,
    Download,
    Map,
    Maximize2,
    Minimize2,
    RefreshCw,
    Settings
} from 'lucide-react';
import React, { useState } from 'react';
import { MapZoneStats } from './MapZoneStats';
import { ZoneDrawingMap } from './ZoneDrawingMap';

// =====================================================
// TIPOS Y INTERFACES
// =====================================================

interface Zone {
  id: string;
  name: string;
  description: string;
  zone_type: 'SAFE' | 'EMERGENCY' | 'DANGER' | 'EVACUATION' | 'RESTRICTED';
  geometry: ZoneGeometry;
  is_active: boolean;
}

interface InteractiveZoneMapProps {
  zones: Zone[];
  onZoneCreate: (geometry: ZoneGeometry) => void;
  onZoneEdit: (zone: Zone, newGeometry: ZoneGeometry) => void;
  onZoneDelete: (zoneId: string) => void;
  onRefresh?: () => void;
}

// =====================================================
// COMPONENTE PRINCIPAL
// =====================================================

export function InteractiveZoneMap({
  zones,
  onZoneCreate,
  onZoneEdit,
  onZoneDelete,
  onRefresh
}: InteractiveZoneMapProps) {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [selectedZone, setSelectedZone] = useState<Zone | null>(null);
  const [tempGeometry, setTempGeometry] = useState<ZoneGeometry | null>(null);
  const [activeTab, setActiveTab] = useState('map');

  console.log('🎛️ InteractiveZoneMap rendered with:', {
    zones: zones.length,
    activeTab,
    isFullscreen
  });

  // Manejadores de eventos
  const handleZoneCreate = (geometry: ZoneGeometry) => {
    setTempGeometry(geometry);
    onZoneCreate(geometry);
  };

  const handleZoneEdit = (zone: Zone, newGeometry: ZoneGeometry) => {
    setSelectedZone(zone);
    onZoneEdit(zone, newGeometry);
  };

  const handleZoneSelect = (zone: Zone) => {
    setSelectedZone(zone);
  };

  const handleExportZones = () => {
    const dataStr = JSON.stringify(zones, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `zonas-seguridad-${new Date().toISOString().split('T')[0]}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  const containerClass = isFullscreen 
    ? 'fixed inset-0 z-50 bg-white' 
    : 'h-full';

  return (
    <div className={containerClass}>
      <Card className="h-full">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Map className="h-5 w-5" />
              Mapa Interactivo de Zonas de Seguridad
            </CardTitle>
            
            <div className="flex items-center gap-2">
              {/* Estadísticas rápidas */}
              <div className="hidden md:flex items-center gap-3 mr-4">
                <Badge variant="outline" className="text-xs">
                  {zones.length} zonas
                </Badge>
                <Badge variant="outline" className="text-xs text-green-600">
                  {zones.filter(z => z.is_active).length} activas
                </Badge>
              </div>

              {/* Controles */}
              <Button
                variant="outline"
                size="sm"
                onClick={onRefresh}
                className="hidden sm:flex"
              >
                <RefreshCw className="h-4 w-4 mr-1" />
                Actualizar
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={handleExportZones}
                className="hidden sm:flex"
              >
                <Download className="h-4 w-4 mr-1" />
                Exportar
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsFullscreen(!isFullscreen)}
              >
                {isFullscreen ? (
                  <Minimize2 className="h-4 w-4" />
                ) : (
                  <Maximize2 className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-0 h-[calc(100%-80px)]">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full">
            <div className="px-6 pb-4">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="map" className="flex items-center gap-2">
                  <Map className="h-4 w-4" />
                  Mapa
                </TabsTrigger>
                <TabsTrigger value="stats" className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  Estadísticas
                </TabsTrigger>
                <TabsTrigger value="split" className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Vista Dividida
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="map" className="h-[calc(100%-60px)] px-6 pb-6">
              <ZoneDrawingMap
                zones={zones}
                onZoneCreate={handleZoneCreate}
                onZoneEdit={handleZoneEdit}
                onZoneDelete={onZoneDelete}
              />
            </TabsContent>

            <TabsContent value="stats" className="h-[calc(100%-60px)] px-6 pb-6">
              <div className="h-full overflow-y-auto">
                <MapZoneStats
                  zones={zones}
                  selectedZone={selectedZone}
                  tempGeometry={tempGeometry}
                />
              </div>
            </TabsContent>

            <TabsContent value="split" className="h-[calc(100%-60px)] px-6 pb-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 h-full">
                {/* Mapa */}
                <div className="lg:col-span-2">
                  <ZoneDrawingMap
                    zones={zones}
                    onZoneCreate={handleZoneCreate}
                    onZoneEdit={handleZoneEdit}
                    onZoneDelete={onZoneDelete}
                  />
                </div>
                
                {/* Panel lateral con estadísticas */}
                <div className="overflow-y-auto">
                  <MapZoneStats
                    zones={zones}
                    selectedZone={selectedZone}
                    tempGeometry={tempGeometry}
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Overlay de ayuda para pantalla completa */}
      {isFullscreen && (
        <div className="absolute top-4 left-4 bg-black/70 text-white px-3 py-2 rounded-lg text-sm">
          Presiona ESC o haz clic en el botón minimizar para salir de pantalla completa
        </div>
      )}
    </div>
  );
}

export default InteractiveZoneMap;
