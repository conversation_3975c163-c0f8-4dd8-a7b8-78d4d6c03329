/**
 * 🔧 Sistema de Debugging Avanzado para Volcano App Frontend
 * Herramientas para capturar, analizar y reportar errores del cliente y servidor
 */

// Tipos para el sistema de debugging
interface DebugLog {
  timestamp: string;
  level: 'error' | 'warn' | 'info' | 'debug';
  category: 'react' | 'api' | 'ui' | 'network' | 'general';
  message: string;
  data?: any;
  stack?: string;
  userAgent?: string;
  url?: string;
}

interface ErrorReport {
  id: string;
  timestamp: string;
  type: 'javascript' | 'react' | 'network' | 'ui';
  message: string;
  stack?: string;
  componentStack?: string;
  props?: any;
  state?: any;
  url: string;
  userAgent: string;
  buildVersion?: string;
}

class DebugManager {
  private logs: DebugLog[] = [];
  private maxLogs = 1000;
  private isEnabled = true;
  private errorReports: ErrorReport[] = [];

  constructor() {
    this.isEnabled = import.meta.env.DEV || import.meta.env.VITE_DEBUG === 'true';
    this.setupGlobalErrorHandlers();
    this.setupConsoleInterception();
  }

  /**
   * Configurar manejadores globales de errores
   */
  private setupGlobalErrorHandlers() {
    // Errores JavaScript no capturados
    window.addEventListener('error', (event) => {
      this.captureError({
        type: 'javascript',
        message: event.message,
        stack: event.error?.stack,
        url: event.filename || window.location.href,
        userAgent: navigator.userAgent,
        buildVersion: (window as any).__APP_VERSION__,
      });
    });

    // Promesas rechazadas no capturadas
    window.addEventListener('unhandledrejection', (event) => {
      this.captureError({
        type: 'javascript',
        message: `Unhandled Promise Rejection: ${event.reason}`,
        stack: event.reason?.stack,
        url: window.location.href,
        userAgent: navigator.userAgent,
        buildVersion: (window as any).__APP_VERSION__,
      });
    });
  }

  /**
   * Interceptar console para capturar logs
   */
  private setupConsoleInterception() {
    if (!this.isEnabled) return;

    const originalConsole = { ...console };

    ['error', 'warn', 'info', 'log'].forEach((level) => {
      (console as any)[level] = (...args: any[]) => {
        // Llamar al console original
        (originalConsole as any)[level](...args);

        // Capturar el log
        this.addLog({
          level: level as any,
          category: 'general',
          message: args.map(arg =>
            typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
          ).join(' '),
          data: args.length === 1 && typeof args[0] === 'object' ? args[0] : args,
        });
      };
    });
  }

  /**
   * Agregar un log al sistema
   */
  addLog(log: Omit<DebugLog, 'timestamp'>) {
    if (!this.isEnabled) return;

    const fullLog: DebugLog = {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      ...log,
    };

    this.logs.push(fullLog);

    // Mantener solo los últimos logs
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Log crítico - también enviarlo como error report
    if (log.level === 'error') {
      this.captureError({
        type: 'javascript',
        message: log.message,
        stack: log.stack,
        url: window.location.href,
        userAgent: navigator.userAgent,
        buildVersion: (window as any).__APP_VERSION__,
      });
    }
  }

  /**
   * Capturar un error y crear reporte
   */
  captureError(error: Omit<ErrorReport, 'id' | 'timestamp'>) {
    const report: ErrorReport = {
      id: `error_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      timestamp: new Date().toISOString(),
      ...error,
    };

    this.errorReports.push(report);

    // Mantener solo los últimos 100 reportes
    if (this.errorReports.length > 100) {
      this.errorReports = this.errorReports.slice(-100);
    }

    // En desarrollo, mostrar en consola
    if (this.isEnabled) {
      console.group(`🚨 Error Report: ${report.id}`);
      console.error('Message:', report.message);
      console.error('Type:', report.type);
      console.error('URL:', report.url);
      if (report.stack) console.error('Stack:', report.stack);
      if (report.componentStack) console.error('Component Stack:', report.componentStack);
      console.groupEnd();
    }
  }

  /**
   * Obtener todos los logs
   */
  getLogs(filter?: { level?: string; category?: string; since?: string }) {
    let filteredLogs = [...this.logs];

    if (filter) {
      if (filter.level) {
        filteredLogs = filteredLogs.filter(log => log.level === filter.level);
      }
      if (filter.category) {
        filteredLogs = filteredLogs.filter(log => log.category === filter.category);
      }
      if (filter.since) {
        filteredLogs = filteredLogs.filter(log => log.timestamp >= filter.since!);
      }
    }

    return filteredLogs;
  }

  /**
   * Obtener reportes de errores
   */
  getErrorReports() {
    return [...this.errorReports];
  }

  /**
   * Limpiar logs
   */
  clearLogs() {
    this.logs = [];
    this.errorReports = [];
  }

  /**
   * Exportar logs para análisis
   */
  exportLogs() {
    const exportData = {
      timestamp: new Date().toISOString(),
      buildVersion: (window as any).__APP_VERSION__,
      userAgent: navigator.userAgent,
      url: window.location.href,
      logs: this.logs,
      errorReports: this.errorReports,
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json',
    });

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `volcano-app-debug-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  /**
   * Obtener estadísticas de debugging
   */
  getStats() {
    const errorCount = this.logs.filter(log => log.level === 'error').length;
    const warnCount = this.logs.filter(log => log.level === 'warn').length;
    const categories = [...new Set(this.logs.map(log => log.category))];

    return {
      totalLogs: this.logs.length,
      errorCount,
      warnCount,
      categories,
      errorReports: this.errorReports.length,
      isEnabled: this.isEnabled,
    };
  }
}

// Instancia global del debug manager
export const debugManager = new DebugManager();

// Funciones de conveniencia para logging
export const debug = {
  error: (message: string, data?: any, category: DebugLog['category'] = 'general') => {
    debugManager.addLog({ level: 'error', category, message, data, stack: new Error().stack });
  },
  warn: (message: string, data?: any, category: DebugLog['category'] = 'general') => {
    debugManager.addLog({ level: 'warn', category, message, data });
  },
  info: (message: string, data?: any, category: DebugLog['category'] = 'general') => {
    debugManager.addLog({ level: 'info', category, message, data });
  },
  log: (message: string, data?: any, category: DebugLog['category'] = 'general') => {
    debugManager.addLog({ level: 'debug', category, message, data });
  },
};

// Hacer disponible globalmente en desarrollo
if (import.meta.env.DEV) {
  (window as any).volcanoDebug = {
    manager: debugManager,
    debug,
    getLogs: () => debugManager.getLogs(),
    getErrors: () => debugManager.getErrorReports(),
    getStats: () => debugManager.getStats(),
    export: () => debugManager.exportLogs(),
    clear: () => debugManager.clearLogs(),
  };

  console.log('🔧 Volcano Debug Tools disponibles en window.volcanoDebug');
}

export default debugManager;
