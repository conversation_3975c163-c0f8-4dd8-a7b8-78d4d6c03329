/**
 * 🌋 Alert Modal Component - CRUD Modal for Volcano Alerts
 */

import React, { useState, useEffect } from 'react';
import { X, Save, AlertTriangle } from 'lucide-react';

interface Alert {
  id: string;
  title: string;
  message: string;
  alert_level: 'NORMAL' | 'ADVISORY' | 'WATCH' | 'WARNING' | 'EMERGENCY';
  volcano_name: string;
  volcano_lat: number;
  volcano_lng: number;
  is_active: boolean;
  expires_at?: string;
}

interface AlertFormData {
  title: string;
  message: string;
  alert_level: string;
  volcano_name: string;
  volcano_lat: number;
  volcano_lng: number;
  is_active: boolean;
  expires_at?: string;
}

interface AlertModalProps {
  alert?: Alert | null;
  onSave: (id: string, data: AlertFormData) => void | ((data: AlertFormData) => void);
  onClose: () => void;
  isSubmitting: boolean;
}

export function AlertModal({ alert, onSave, onClose, isSubmitting }: AlertModalProps) {
  const [formData, setFormData] = useState<AlertFormData>({
    title: '',
    message: '',
    alert_level: 'NORMAL',
    volcano_name: 'Volcán Villarrica',
    volcano_lat: -39.420000,
    volcano_lng: -71.939167,
    is_active: true,
    expires_at: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (alert) {
      setFormData({
        title: alert.title,
        message: alert.message,
        alert_level: alert.alert_level,
        volcano_name: alert.volcano_name,
        volcano_lat: alert.volcano_lat,
        volcano_lng: alert.volcano_lng,
        is_active: alert.is_active,
        expires_at: alert.expires_at ? alert.expires_at.split('T')[0] : ''
      });
    }
  }, [alert]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'El título es requerido';
    }

    if (!formData.message.trim()) {
      newErrors.message = 'El mensaje es requerido';
    }

    if (!formData.volcano_name.trim()) {
      newErrors.volcano_name = 'El nombre del volcán es requerido';
    }

    if (isNaN(formData.volcano_lat) || formData.volcano_lat < -90 || formData.volcano_lat > 90) {
      newErrors.volcano_lat = 'Latitud inválida';
    }

    if (isNaN(formData.volcano_lng) || formData.volcano_lng < -180 || formData.volcano_lng > 180) {
      newErrors.volcano_lng = 'Longitud inválida';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const submitData = {
      ...formData,
      expires_at: formData.expires_at ? new Date(formData.expires_at).toISOString() : undefined
    };

    if (alert) {
      (onSave as (id: string, data: AlertFormData) => void)(alert.id, submitData);
    } else {
      (onSave as (data: AlertFormData) => void)(submitData);
    }
  };

  const handleChange = (field: keyof AlertFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <AlertTriangle className="h-6 w-6 text-orange-500" />
            <h3 className="text-lg font-medium text-gray-900">
              {alert ? 'Editar Alerta' : 'Nueva Alerta Volcánica'}
            </h3>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
            disabled={isSubmitting}
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Título de la Alerta *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => handleChange('title', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                errors.title ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Ej: Incremento de actividad sísmica"
              disabled={isSubmitting}
            />
            {errors.title && <p className="mt-1 text-sm text-red-600">{errors.title}</p>}
          </div>

          {/* Message */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Mensaje de la Alerta *
            </label>
            <textarea
              value={formData.message}
              onChange={(e) => handleChange('message', e.target.value)}
              rows={4}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                errors.message ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Describe la situación actual del volcán y las recomendaciones..."
              disabled={isSubmitting}
            />
            {errors.message && <p className="mt-1 text-sm text-red-600">{errors.message}</p>}
          </div>

          {/* Alert Level */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nivel de Alerta *
            </label>
            <select
              value={formData.alert_level}
              onChange={(e) => handleChange('alert_level', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              disabled={isSubmitting}
            >
              <option value="NORMAL">🟢 Normal - Sin riesgo aparente</option>
              <option value="ADVISORY">🟡 Aviso - Actividad elevada</option>
              <option value="WATCH">🟠 Vigilancia - Monitoreo intensivo</option>
              <option value="WARNING">🔴 Alerta - Riesgo moderado</option>
              <option value="EMERGENCY">🚨 Emergencia - Riesgo alto</option>
            </select>
          </div>

          {/* Volcano Info */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="md:col-span-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Volcán *
              </label>
              <input
                type="text"
                value={formData.volcano_name}
                onChange={(e) => handleChange('volcano_name', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                  errors.volcano_name ? 'border-red-300' : 'border-gray-300'
                }`}
                disabled={isSubmitting}
              />
              {errors.volcano_name && <p className="mt-1 text-sm text-red-600">{errors.volcano_name}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Latitud *
              </label>
              <input
                type="number"
                step="0.000001"
                value={formData.volcano_lat}
                onChange={(e) => handleChange('volcano_lat', parseFloat(e.target.value))}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                  errors.volcano_lat ? 'border-red-300' : 'border-gray-300'
                }`}
                disabled={isSubmitting}
              />
              {errors.volcano_lat && <p className="mt-1 text-sm text-red-600">{errors.volcano_lat}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Longitud *
              </label>
              <input
                type="number"
                step="0.000001"
                value={formData.volcano_lng}
                onChange={(e) => handleChange('volcano_lng', parseFloat(e.target.value))}
                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                  errors.volcano_lng ? 'border-red-300' : 'border-gray-300'
                }`}
                disabled={isSubmitting}
              />
              {errors.volcano_lng && <p className="mt-1 text-sm text-red-600">{errors.volcano_lng}</p>}
            </div>
          </div>

          {/* Options */}
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="is_active"
                checked={formData.is_active}
                onChange={(e) => handleChange('is_active', e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                disabled={isSubmitting}
              />
              <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
                Alerta activa (visible para usuarios)
              </label>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Fecha de Expiración (Opcional)
              </label>
              <input
                type="date"
                value={formData.expires_at}
                onChange={(e) => handleChange('expires_at', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                disabled={isSubmitting}
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              disabled={isSubmitting}
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="flex items-center space-x-2 px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
            >
              <Save className="h-4 w-4" />
              <span>{isSubmitting ? 'Guardando...' : (alert ? 'Actualizar' : 'Crear Alerta')}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
