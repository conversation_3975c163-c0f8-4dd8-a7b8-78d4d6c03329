/**
 * 🌋 Volcano App Backend - Middleware de Validación
 * Validaciones de entrada para requests HTTP
 */

import { Request, Response, NextFunction } from 'express';
import { body, param, query, validationResult } from 'express-validator';
import { logger, logValidation } from '@/utils/logger';
import { AlertLevel, ZoneType, UserRole, AuthenticatedRequest } from '@/types';

// =====================================================
// MIDDLEWARE DE VALIDACIÓN GENERAL
// =====================================================

/**
 * Middleware para manejar errores de validación
 */
export function handleValidationErrors() {
  return (req: Request, res: Response, next: NextFunction) => {
    const errors = validationResult(req);
    
    if (!errors.isEmpty()) {
      const errorDetails = errors.array().map(error => ({
        field: error.type === 'field' ? error.path : 'unknown',
        message: error.msg,
        value: error.type === 'field' ? error.value : undefined
      }));

      // Log validation errors
      errorDetails.forEach(error => {
        logValidation(
          error.field, 
          error.value, 
          error.message, 
          (req as AuthenticatedRequest).user?.id
        );
      });

      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errorDetails,
        timestamp: new Date()
      });
    }
    
    next();
  };
}

/**
 * Middleware para validar campos requeridos básicos
 */
export function validateRequired(fields: string[]) {
  return (req: Request, res: Response, next: NextFunction) => {
    const missing = fields.filter(field => {
      const value = req.body[field];
      return value === undefined || value === null || value === '';
    });
    
    if (missing.length > 0) {
      return res.status(400).json({
        success: false,
        error: `Missing required fields: ${missing.join(', ')}`,
        timestamp: new Date()
      });
    }
    
    next();
  };
}

// =====================================================
// VALIDACIONES PARA AUTENTICACIÓN
// =====================================================

export const validateLogin = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long'),
  handleValidationErrors()
];

export const validateRefreshToken = [
  body('refresh_token')
    .notEmpty()
    .withMessage('Refresh token is required'),
  handleValidationErrors()
];

export const validateChangePassword = [
  body('current_password')
    .notEmpty()
    .withMessage('Current password is required'),
  body('new_password')
    .isLength({ min: 8 })
    .withMessage('New password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('New password must contain at least one lowercase letter, one uppercase letter, and one number'),
  handleValidationErrors()
];

// =====================================================
// VALIDACIONES PARA ALERTAS
// =====================================================

export const validateCreateAlert = [
  body('title')
    .trim()
    .isLength({ min: 3, max: 200 })
    .withMessage('Title must be between 3 and 200 characters'),
  body('message')
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('Message must be between 10 and 1000 characters'),
  body('alert_level')
    .isIn(Object.values(AlertLevel))
    .withMessage(`Alert level must be one of: ${Object.values(AlertLevel).join(', ')}`),
  body('volcano_name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Volcano name must be between 2 and 100 characters'),
  body('volcano_lat')
    .optional()
    .isFloat({ min: -90, max: 90 })
    .withMessage('Volcano latitude must be between -90 and 90'),
  body('volcano_lng')
    .optional()
    .isFloat({ min: -180, max: 180 })
    .withMessage('Volcano longitude must be between -180 and 180'),
  body('is_scheduled')
    .optional()
    .isBoolean()
    .withMessage('is_scheduled must be a boolean'),
  body('scheduled_for')
    .optional()
    .isISO8601()
    .withMessage('scheduled_for must be a valid ISO 8601 date'),
  body('expires_at')
    .optional()
    .isISO8601()
    .withMessage('expires_at must be a valid ISO 8601 date'),
  handleValidationErrors()
];

export const validateUpdateAlert = [
  param('id')
    .isUUID()
    .withMessage('Alert ID must be a valid UUID'),
  body('title')
    .optional()
    .trim()
    .isLength({ min: 3, max: 200 })
    .withMessage('Title must be between 3 and 200 characters'),
  body('message')
    .optional()
    .trim()
    .isLength({ min: 10, max: 1000 })
    .withMessage('Message must be between 10 and 1000 characters'),
  body('alert_level')
    .optional()
    .isIn(Object.values(AlertLevel))
    .withMessage(`Alert level must be one of: ${Object.values(AlertLevel).join(', ')}`),
  body('volcano_name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Volcano name must be between 2 and 100 characters'),
  body('volcano_lat')
    .optional()
    .isFloat({ min: -90, max: 90 })
    .withMessage('Volcano latitude must be between -90 and 90'),
  body('volcano_lng')
    .optional()
    .isFloat({ min: -180, max: 180 })
    .withMessage('Volcano longitude must be between -180 and 180'),
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('is_active must be a boolean'),
  body('is_scheduled')
    .optional()
    .isBoolean()
    .withMessage('is_scheduled must be a boolean'),
  body('scheduled_for')
    .optional()
    .isISO8601()
    .withMessage('scheduled_for must be a valid ISO 8601 date'),
  body('expires_at')
    .optional()
    .isISO8601()
    .withMessage('expires_at must be a valid ISO 8601 date'),
  handleValidationErrors()
];

export const validateAlertId = [
  param('id')
    .isUUID()
    .withMessage('Alert ID must be a valid UUID'),
  handleValidationErrors()
];

// =====================================================
// VALIDACIONES PARA QUERY PARAMETERS
// =====================================================

export const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  query('sort_by')
    .optional()
    .isAlpha()
    .withMessage('Sort field must contain only letters'),
  query('sort_order')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc'),
  handleValidationErrors()
];

export const validateSearch = [
  query('search')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Search term must be between 2 and 100 characters'),
  handleValidationErrors()
];

// =====================================================
// VALIDACIONES PARA ZONAS DE SEGURIDAD
// =====================================================

export const validateCreateZone = [
  body('name')
    .trim()
    .isLength({ min: 3, max: 100 })
    .withMessage('Zone name must be between 3 and 100 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description must not exceed 500 characters'),
  body('zone_type')
    .isIn(Object.values(ZoneType))
    .withMessage(`Zone type must be one of: ${Object.values(ZoneType).join(', ')}`),
  body('geometry')
    .notEmpty()
    .withMessage('Geometry is required')
    .custom((value) => {
      if (!value.type || !value.coordinates) {
        throw new Error('Geometry must have type and coordinates');
      }
      return true;
    }),
  body('capacity')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Capacity must be a positive integer'),
  handleValidationErrors()
];

export const validateUpdateZone = [
  param('id')
    .isUUID()
    .withMessage('Zone ID must be a valid UUID'),
  body('name')
    .optional()
    .trim()
    .isLength({ min: 3, max: 100 })
    .withMessage('Zone name must be between 3 and 100 characters'),
  body('description')
    .optional()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description must not exceed 500 characters'),
  body('zone_type')
    .optional()
    .isIn(Object.values(ZoneType))
    .withMessage(`Zone type must be one of: ${Object.values(ZoneType).join(', ')}`),
  body('capacity')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Capacity must be a positive integer'),
  body('is_active')
    .optional()
    .isBoolean()
    .withMessage('is_active must be a boolean'),
  handleValidationErrors()
];

export const validateZoneId = [
  param('id')
    .isUUID()
    .withMessage('Zone ID must be a valid UUID'),
  handleValidationErrors()
];

// =====================================================
// VALIDACIONES PARA PARÁMETROS COMUNES
// =====================================================

export const validateUUID = (paramName: string = 'id') => [
  param(paramName)
    .isUUID()
    .withMessage(`${paramName} must be a valid UUID`),
  handleValidationErrors()
];

// =====================================================
// EXPORTACIONES
// =====================================================

export default {
  handleValidationErrors,
  validateRequired,
  validateLogin,
  validateRefreshToken,
  validateChangePassword,
  validateCreateAlert,
  validateUpdateAlert,
  validateAlertId,
  validatePagination,
  validateSearch,
  validateCreateZone,
  validateUpdateZone,
  validateZoneId,
  validateUUID
};
